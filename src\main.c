#include <STC89C5xRC.H>
#include "Int_KeyMaterix.h"
#include "Int_DigitalTube.h"
#include "Int_Buzzer.h"

// 蜂鸣器控制引脚定义
#define BUZZER P46

void main()
{
    u8 key;
    u8 last_key = 0;  // 记录上一次按键值
    u8 i;             // 蜂鸣器控制变量

    // 初始化数码管
    Int_DigitalTube_Init();

    // 显示初始值0
    Int_DigitalTube_DisplayNum(0);

    while (1) {

        // 扫描矩阵按键
        key = Int_KeyMatrix_CheckSW();

        // 只有当按键值改变时才更新显示和触发蜂鸣器
        if (key && key != last_key) {
            // 在数码管上显示按键值
            Int_DigitalTube_DisplayNum(key);

            // 触发蜂鸣器：发出短促的"嘀"声
            for (i = 0; i < 100; i++) {
                BUZZER = ~BUZZER;        // 翻转蜂鸣器状态
                Com_Util_Delay1ms(1);   // 延时1ms，产生约500Hz音频
            }
            BUZZER = 1;  // 确保蜂鸣器关闭

            last_key = key;
        }

        // 刷新数码管显示
        Int_DigitalTube_Refresh();
    }
}