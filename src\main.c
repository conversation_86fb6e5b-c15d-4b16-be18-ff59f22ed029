#include <STC89C5xRC.H>
#include "Int_KeyMaterix.h"
#include "Int_DigitalTube.h"

void main()
{
    u8 key;
    u8 last_key = 0;  // 记录上一次按键值

    // 初始化数码管
    Int_DigitalTube_Init();

    // 显示初始值0
    Int_DigitalTube_DisplayNum(0);

    while (1) {
        // 扫描矩阵按键
        key = Int_KeyMatrix_CheckSW();

        // 只有当按键值改变时才更新显示
        if (key && key != last_key) {
            // 在数码管上显示按键值
            Int_DigitalTube_DisplayNum(key);
            last_key = key;
        }

        // 刷新数码管显示
        Int_DigitalTube_Refresh();
    }
}