
#include <reg52.h>
#include "config.h"

// 延时函数
void delay_ms(unsigned int ms)
{
    unsigned int i, j;
    for(i = 0; i < ms; i++)
        for(j = 0; j < 123; j++);
}

// 系统初始化
void system_init(void)
{
    // 初始化IO口
    P1 = 0xFF;
}

void main()
{
    system_init();

    while (1) {
        LED = 0;        // 点亮LED
        delay_ms(500);
        LED = 1;        // 熄灭LED
        delay_ms(500);
    }
}