#include "Int_KeyMaterix.h"
#include <STC89C5xRC.H>
u8 INT_KEYMATERIX_CheckSW()
u8 Int_KeyMatrix_CheckSW()
{
    // ����1��
    P2 = 0xFE;
    if (P24 == 0) {
        Com_Util_Delay1ms(10);
        if (P24 == 0) {
            while (P24 == 0);
            return 5;
        }
    }

    if (P25 == 0) {
        Com_Util_Delay1ms(10);
        if (P25 == 0) {
            while (P25 == 0);
            return 6;
        }
    }

    if (P26 == 0) {
        Com_Util_Delay1ms(10);
        if (P26 == 0) {
            while (P26 == 0);
            return 7;
        }
    }

    if (P27 == 0) {
        Com_Util_Delay1ms(10);
        if (P27 == 0) {
            while (P27 == 0);
            return 8;
        }
    }

    // ����2��
    P2 = 0xFD;
    if (P24 == 0) {
        Com_Util_Delay1ms(10);
        if (P24 == 0) {
            while (P24 == 0);
            return 9;
        }
    }

    if (P25 == 0) {
        Com_Util_Delay1ms(10);
        if (P25 == 0) {
            while (P25 == 0);
            return 10;
        }
    }

    if (P26 == 0) {
        Com_Util_Delay1ms(10);
        if (P26 == 0) {
            while (P26 == 0);
            return 11;
        }
    }

    if (P27 == 0) {
        Com_Util_Delay1ms(10);
        if (P27 == 0) {
            while (P27 == 0);
            return 12;
        }
    }

    // ����3��
    P2 = 0xFB;
    if (P24 == 0) {
        Com_Util_Delay1ms(10);
        if (P24 == 0) {
            while (P24 == 0);
            return 13;
        }
    }

    if (P25 == 0) {
        Com_Util_Delay1ms(10);
        if (P25 == 0) {
            while (P25 == 0);
            return 14;
        }
    }

    if (P26 == 0) {
        Com_Util_Delay1ms(10);
        if (P26 == 0) {
            while (P26 == 0);
            return 15;
        }
    }

    if (P27 == 0) {
        Com_Util_Delay1ms(10);
        if (P27 == 0) {
            while (P27 == 0);
            return 16;
        }
    }

    // ����4��
    P2 = 0xF7;
    if (P24 == 0) {
        Com_Util_Delay1ms(10);
        if (P24 == 0) {
            while (P24 == 0);
            return 17;
        }
    }

    if (P25 == 0) {
        Com_Util_Delay1ms(10);
        if (P25 == 0) {
            while (P25 == 0);
            return 18;
        }
    }

    if (P26 == 0) {
        Com_Util_Delay1ms(10);
        if (P26 == 0) {
            while (P26 == 0);
            return 19;
        }
    }

    if (P27 == 0) {
        Com_Util_Delay1ms(10);
        if (P27 == 0) {
            while (P27 == 0);
            return 20;
        }
    }
    return 0;
}

u8 Int_KeyMatrix_CheckSW()
{
    u8 i;
    u8 lines[4] = {0xFE, 0xFD, 0xFB, 0xF7};
    for (i = 0; i < 4; i++) {
        P2 = lines[i];
        if (P24 == 0) {
            Com_Util_Delay1ms(10);
            if (P24 == 0) {
                while (P24 == 0);
                return 5 + 4 * i;
            }
        }

        if (P25 == 0) {
            Com_Util_Delay1ms(10);
            if (P25 == 0) {
                while (P25 == 0);
                return 6 + 4 * i;
            }
        }

        if (P26 == 0) {
            Com_Util_Delay1ms(10);
            if (P26 == 0) {
                while (P26 == 0);
                return 7 + 4 * i;
            }
        }

        if (P27 == 0) {
            Com_Util_Delay1ms(10);
            if (P27 == 0) {
                while (P27 == 0);
                return 8 + 4 * i;
            }
        }
    }
    return 0;
}
