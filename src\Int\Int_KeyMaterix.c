#include "Int_KeyMaterix.h"
#include <STC89C5xRC.H>

/**
 * @brief 4x4矩阵按键扫描函数
 * @return 按键值：1-16对应16个按键，0表示无按键按下
 *
 * 矩阵按键布局：
 * 1  2  3  4
 * 5  6  7  8
 * 9  10 11 12
 * 13 14 15 16
 */

u8 Int_KeyMatrix_CheckSW()
{
    u8 i;
    u8 lines[4] = {0xFE, 0xFD, 0xFB, 0xF7};  // 行扫描线：P20-P23

    for (i = 0; i < 4; i++) {
        P2 = lines[i];  // 设置当前行为低电平

        // 检查第1列 (P24)
        if (P24 == 0) {
            Com_Util_Delay1ms(10);  // 消抖延时
            if (P24 == 0) {
                while (P24 == 0);  // 等待按键释放
                return 1 + 4 * i;   // 返回按键值：1,5,9,13
            }
        }

        // 检查第2列 (P25)
        if (P25 == 0) {
            Com_Util_Delay1ms(10);
            if (P25 == 0) {
                while (P25 == 0);
                return 2 + 4 * i;   // 返回按键值：2,6,10,14
            }
        }

        // 检查第3列 (P26)
        if (P26 == 0) {
            Com_Util_Delay1ms(10);
            if (P26 == 0) {
                while (P26 == 0);
                return 3 + 4 * i;   // 返回按键值：3,7,11,15
            }
        }

        // 检查第4列 (P27)
        if (P27 == 0) {
            Com_Util_Delay1ms(10);
            if (P27 == 0) {
                while (P27 == 0);
                return 4 + 4 * i;   // 返回按键值：4,8,12,16
            }
        }
    }
    return 0;  // 无按键按下
}
